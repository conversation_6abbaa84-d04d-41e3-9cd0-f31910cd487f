<?php $__env->startSection('title', $metaTags['title'] ?? $page->title); ?>
<?php $__env->startSection('description', $metaTags['description'] ?? ''); ?>

<?php if(isset($metaTags['keywords'])): ?>
<?php $__env->startSection('keywords', $metaTags['keywords']); ?>
<?php endif; ?>

<?php $__env->startPush('meta'); ?>
<?php echo App\Helpers\SeoHelper::renderMetaTags($metaTags); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Modern Contact Hero Section -->
<section class="py-5 bg-gradient-to-r from-pink-50 to-purple-50" style="background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%); min-height: 40vh;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="font-playfair display-4 fw-bold text-dark mb-4"><?php echo e($page->title); ?></h1>
                <?php if($page->excerpt): ?>
                <p class="lead text-muted mb-4"><?php echo e($page->excerpt); ?></p>
                <?php endif; ?>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="tel:+919876543210" class="btn btn-primary-pink btn-lg">
                        <i class="fas fa-phone me-2"></i>Call Now
                    </a>
                    <a href="https://wa.me/919876543210?text=Hello, I would like to know more about your jewelry collection"
                       target="_blank" class="btn btn-success btn-lg">
                        <i class="fab fa-whatsapp me-2"></i>WhatsApp
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <img src="https://images.unsplash.com/photo-1573408301185-9146fe634ad0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                         alt="Contact Us" class="img-fluid rounded-3 shadow-lg" style="max-height: 400px; object-fit: cover;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information Cards -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                    <div class="card-body p-4">
                        <div class="bg-primary-pink text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-phone fa-2x"></i>
                        </div>
                        <h4 class="font-playfair mb-3">Call Us</h4>
                        <p class="text-muted mb-3">Speak directly with our jewelry experts</p>
                        <h5 class="text-primary-pink mb-3">+ 91 9753447832</h5>
                        <p class="small text-muted">Mon - Sat: 10:00 AM - 8:00 PM<br>Sunday: 11:00 AM - 6:00 PM</p>
                        <a href="tel:+919876543210" class="btn btn-outline-primary-pink">
                            <i class="fas fa-phone me-2"></i>Call Now
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                    <div class="card-body p-4">
                        <div class="bg-primary-pink text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                        <h4 class="font-playfair mb-3">Email Us</h4>
                        <p class="text-muted mb-3">Get detailed information about our products</p>
                        <h5 class="text-primary-pink mb-3"><EMAIL></h5>
                        <p class="small text-muted">We respond within 24 hours<br>Professional consultation available</p>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary-pink">
                            <i class="fas fa-envelope me-2"></i>Send Email
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                    <div class="card-body p-4">
                        <div class="bg-primary-pink text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                        <h4 class="font-playfair mb-3">Visit Our Store</h4>
                        <p class="text-muted mb-3">Experience our jewelry collection in person</p>
                        <h5 class="text-primary-pink mb-3">ShreeJi Jewelry</h5>
                        <p class="small text-muted">123 Jewelry Street<br>Mumbai, Maharashtra 400001<br>India</p>
                        <a href="https://maps.google.com" target="_blank" class="btn btn-outline-primary-pink">
                            <i class="fas fa-map-marker-alt me-2"></i>Get Directions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5">
                    <h2 class="font-playfair display-5 mb-3">Send Us a Message</h2>
                    <p class="text-muted">Have a question about our jewelry? We'd love to hear from you.</p>
                </div>
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <form id="contactForm">
                            <?php echo csrf_field(); ?>
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control form-control-lg" id="name" name="name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control form-control-lg" id="email" name="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control form-control-lg" id="phone" name="phone">
                                </div>
                                <div class="col-md-6">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <select class="form-select form-select-lg" id="subject" name="subject" required>
                                        <option value="">Select a subject</option>
                                        <option value="product-inquiry">Product Inquiry</option>
                                        <option value="custom-order">Custom Order</option>
                                        <option value="repair-service">Repair Service</option>
                                        <option value="general">General Question</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control" id="message" name="message" rows="5"
                                              placeholder="Tell us about your jewelry needs..." required></textarea>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                        <label class="form-check-label" for="newsletter">
                                            I would like to receive updates about new collections and special offers
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary-pink btn-lg px-5">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Social Media & Additional Info -->
<section class="py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="font-playfair mb-4">Follow Us on Social Media</h3>
                <p class="text-muted mb-4">Stay updated with our latest collections, behind-the-scenes content, and jewelry care tips.</p>
                <div class="d-flex gap-3">
                    <a href="#" class="btn btn-outline-primary btn-lg rounded-circle" style="width: 60px; height: 60px;">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="btn btn-outline-danger btn-lg rounded-circle" style="width: 60px; height: 60px;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="btn btn-outline-info btn-lg rounded-circle" style="width: 60px; height: 60px;">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="btn btn-outline-success btn-lg rounded-circle" style="width: 60px; height: 60px;">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card border-0 bg-primary-pink text-white">
                    <div class="card-body p-4">
                        <h4 class="mb-3">
                            <i class="fas fa-gem me-2"></i>Why Choose ShreeJi?
                        </h4>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check me-2"></i>Certified authentic jewelry</li>
                            <li class="mb-2"><i class="fas fa-check me-2"></i>Expert craftsmanship</li>
                            <li class="mb-2"><i class="fas fa-check me-2"></i>Lifetime warranty</li>
                            <li class="mb-2"><i class="fas fa-check me-2"></i>Free cleaning & maintenance</li>
                            <li class="mb-0"><i class="fas fa-check me-2"></i>Custom design services</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Page Content (if any additional content from CMS) -->
<?php if($page->content && strip_tags($page->content)): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="content">
                    <?php echo $page->content; ?>

                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Structured Data -->
<?php $__env->startPush('structured-data'); ?>
<?php echo App\Helpers\SeoHelper::renderStructuredData($structuredData); ?>

<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.content h2 {
    font-family: 'Playfair Display', serif;
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.content h3 {
    font-family: 'Playfair Display', serif;
    color: #34495e;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.content p {
    margin-bottom: 1.25rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary-pink);
    box-shadow: 0 0 0 0.2rem rgba(219, 112, 147, 0.25);
}

.btn-primary-pink {
    background-color: #db7093;
    border-color: #db7093;
}

.btn-primary-pink:hover {
    background-color: #c85a7a;
    border-color: #c85a7a;
}

.btn-outline-primary-pink {
    color: #db7093;
    border-color: #db7093;
}

.btn-outline-primary-pink:hover {
    background-color: #db7093;
    border-color: #db7093;
    color: white;
}

.text-primary-pink {
    color: #db7093 !important;
}

.bg-primary-pink {
    background-color: #db7093 !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const form = this;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';

    // Simulate form submission (replace with actual endpoint)
    setTimeout(() => {
        // Show success message
        showAlert('Thank you for your message! We will get back to you within 24 hours.', 'success');

        // Reset form
        form.reset();

        // Reset button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }, 2000);
});

function showAlert(message, type) {
    // Remove any existing alerts
    document.querySelectorAll('.custom-alert').forEach(alert => alert.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed custom-alert`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px; max-width: 500px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';

    const icon = type === 'success' ? 'fas fa-check-circle text-success' : 'fas fa-exclamation-triangle text-danger';
    const title = type === 'success' ? 'Success!' : 'Error!';

    alertDiv.innerHTML = `
        <div class="d-flex align-items-start">
            <i class="${icon} me-3 fs-5 mt-1"></i>
            <div class="flex-grow-1">
                <strong class="d-block mb-1">${title}</strong>
                <div style="font-size: 0.9em;">${message}</div>
            </div>
            <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 150);
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\www\KanhaShop\resources\views/pages/templates/contact.blade.php ENDPATH**/ ?>