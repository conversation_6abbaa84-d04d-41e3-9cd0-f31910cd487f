@extends('layouts.app')

@section('title', 'Track Your Order - ShreeJi Jewelry')
@section('description', 'Track your ShreeJi Jewelry order status and delivery information using your order number or tracking number.')

@section('content')
<!-- Order Tracking Hero Section -->
<section class="py-5 bg-gradient-to-r from-blue-50 to-indigo-50" style="background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="font-playfair display-4 fw-bold text-dark mb-4">Track Your Order</h1>
                <p class="lead text-muted mb-4">Enter your order number or tracking number to get real-time updates on your jewelry order.</p>
                <div class="d-flex align-items-center text-muted">
                    <i class="fas fa-shield-alt text-primary me-2"></i>
                    <small>Secure tracking • Real-time updates • 24/7 support</small>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <img src="https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                         alt="Order Tracking" class="img-fluid rounded-3 shadow-lg" style="max-height: 300px; object-fit: cover;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Order Search Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-search text-primary fa-3x mb-3"></i>
                            <h3 class="font-playfair mb-3">Find Your Order</h3>
                            <p class="text-muted">Enter your order number (e.g., ORD-2024-001) or tracking number</p>
                        </div>
                        
                        <form id="trackingForm">
                            @csrf
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="searchInput" 
                                           name="search" 
                                           placeholder="Enter order number or tracking number"
                                           required>
                                    <div class="invalid-feedback" id="searchError"></div>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary btn-lg w-100" id="trackBtn">
                                        <i class="fas fa-search me-2"></i>Track Order
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Can't find your order number? Check your email confirmation or 
                                <a href="{{ route('page.show', 'contact') }}" class="text-decoration-none">contact us</a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Order Details Section (Hidden by default) -->
<section class="py-5 bg-light" id="orderDetailsSection" style="display: none;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-0">
                        <!-- Order Header -->
                        <div class="bg-primary text-white p-4">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h4 class="mb-1" id="orderNumber">Order #ORD-2024-001</h4>
                                    <p class="mb-0 opacity-75" id="orderDate">Placed on Dec 15, 2024</p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <span class="badge bg-light text-dark fs-6 px-3 py-2" id="orderStatus">Processing</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Order Progress -->
                        <div class="p-4">
                            <h5 class="font-playfair mb-4">Order Progress</h5>
                            <div id="orderProgress">
                                <!-- Progress steps will be inserted here -->
                            </div>
                        </div>
                        
                        <!-- Tracking Information -->
                        <div class="p-4 bg-light" id="trackingInfo" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold mb-2">Tracking Information</h6>
                                    <p class="mb-1"><strong>Tracking Number:</strong> <span id="trackingNumber">-</span></p>
                                    <p class="mb-1"><strong>Courier Service:</strong> <span id="courierService">-</span></p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <a href="#" id="trackingUrl" target="_blank" class="btn btn-outline-primary" style="display: none;">
                                        <i class="fas fa-external-link-alt me-2"></i>Track on Courier Website
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Order Items -->
                        <div class="p-4">
                            <h5 class="font-playfair mb-4">Order Items</h5>
                            <div id="orderItems">
                                <!-- Order items will be inserted here -->
                            </div>
                        </div>
                        
                        <!-- Delivery Address -->
                        <div class="p-4 bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold mb-3">Delivery Address</h6>
                                    <div id="shippingAddress">
                                        <!-- Address will be inserted here -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold mb-3">Order Summary</h6>
                                    <div id="orderSummary">
                                        <!-- Summary will be inserted here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Help Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-phone fa-lg"></i>
                    </div>
                    <h5>Call Us</h5>
                    <p class="text-muted mb-3">Speak with our customer service team</p>
                    <a href="tel:+919876543210" class="btn btn-outline-primary">
                        <i class="fas fa-phone me-2"></i>+ 91 9753447832
                    </a>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="text-center">
                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="fab fa-whatsapp fa-lg"></i>
                    </div>
                    <h5>WhatsApp</h5>
                    <p class="text-muted mb-3">Get instant support via WhatsApp</p>
                    <a href="https://wa.me/919876543210?text=Hi, I need help tracking my order" target="_blank" class="btn btn-outline-success">
                        <i class="fab fa-whatsapp me-2"></i>Chat Now
                    </a>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="text-center">
                    <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-envelope fa-lg"></i>
                    </div>
                    <h5>Email Support</h5>
                    <p class="text-muted mb-3">Send us your tracking queries</p>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-info">
                        <i class="fas fa-envelope me-2"></i>Email Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.progress-step {
    position: relative;
    padding-left: 3rem;
    padding-bottom: 2rem;
}

.progress-step:last-child {
    padding-bottom: 0;
}

.progress-step::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 2.5rem;
    bottom: -1rem;
    width: 2px;
    background-color: #e9ecef;
}

.progress-step:last-child::before {
    display: none;
}

.progress-step.completed::before {
    background-color: #28a745;
}

.progress-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
    color: #6c757d;
    font-size: 0.875rem;
    z-index: 1;
}

.progress-step.completed .progress-icon {
    background-color: #28a745;
    color: white;
}

.progress-step.current .progress-icon {
    background-color: #007bff;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

.order-item {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
}

.order-item:last-child {
    border-bottom: none;
}

.order-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 0.375rem;
}
</style>
@endpush

@push('scripts')
<script>
document.getElementById('trackingForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const form = this;
    const formData = new FormData(form);
    const trackBtn = document.getElementById('trackBtn');
    const searchInput = document.getElementById('searchInput');
    const searchError = document.getElementById('searchError');
    const originalText = trackBtn.innerHTML;

    // Clear previous errors
    searchInput.classList.remove('is-invalid');
    searchError.textContent = '';

    // Show loading state
    trackBtn.disabled = true;
    trackBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Tracking...';

    fetch('{{ route("order-tracking.track") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayOrderDetails(data.order);
            document.getElementById('orderDetailsSection').style.display = 'block';
            document.getElementById('orderDetailsSection').scrollIntoView({ behavior: 'smooth' });
        } else {
            searchInput.classList.add('is-invalid');
            searchError.textContent = data.message;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        searchInput.classList.add('is-invalid');
        searchError.textContent = 'Unable to track order. Please check your connection and try again.';
    })
    .finally(() => {
        trackBtn.disabled = false;
        trackBtn.innerHTML = originalText;
    });
});

function displayOrderDetails(order) {
    // Update order header
    document.getElementById('orderNumber').textContent = `Order #${order.order_number}`;
    document.getElementById('orderDate').textContent = `Placed on ${order.created_at}`;
    document.getElementById('orderStatus').textContent = order.status_display;

    // Update order progress
    displayOrderProgress(order.status_history);

    // Update tracking information
    if (order.tracking_number) {
        document.getElementById('trackingInfo').style.display = 'block';
        document.getElementById('trackingNumber').textContent = order.tracking_number;
        document.getElementById('courierService').textContent = order.courier_service || 'Speed Post';

        if (order.tracking_url) {
            const trackingUrl = document.getElementById('trackingUrl');
            trackingUrl.href = order.tracking_url;
            trackingUrl.style.display = 'inline-block';
        }
    }

    // Update order items
    displayOrderItems(order.items);

    // Update addresses and summary
    displayAddressAndSummary(order);
}

function displayOrderProgress(statusHistory) {
    const progressContainer = document.getElementById('orderProgress');
    progressContainer.innerHTML = '';

    statusHistory.forEach((step, index) => {
        const stepDiv = document.createElement('div');
        stepDiv.className = `progress-step ${step.completed ? 'completed' : ''}`;

        stepDiv.innerHTML = `
            <div class="progress-icon">
                <i class="${step.icon}"></i>
            </div>
            <div>
                <h6 class="mb-1">${step.title}</h6>
                <p class="text-muted mb-1">${step.description}</p>
                <small class="text-muted">${step.timestamp}</small>
            </div>
        `;

        progressContainer.appendChild(stepDiv);
    });
}

function displayOrderItems(items) {
    const itemsContainer = document.getElementById('orderItems');
    itemsContainer.innerHTML = '';

    items.forEach(item => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'order-item';

        itemDiv.innerHTML = `
            <div class="row align-items-center">
                <div class="col-auto">
                    <img src="${item.image || 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'}"
                         alt="${item.name}" class="rounded">
                </div>
                <div class="col">
                    <h6 class="mb-1">${item.name}</h6>
                    <p class="text-muted mb-1">SKU: ${item.sku}</p>
                    ${item.size ? `<p class="text-muted mb-0">Size: ${item.size}</p>` : ''}
                </div>
                <div class="col-auto text-end">
                    <p class="mb-1">Qty: ${item.quantity}</p>
                    <p class="mb-0 fw-bold">₹${parseFloat(item.total).toLocaleString()}</p>
                </div>
            </div>
        `;

        itemsContainer.appendChild(itemDiv);
    });
}

function displayAddressAndSummary(order) {
    // Display shipping address
    const shippingDiv = document.getElementById('shippingAddress');
    const shipping = order.shipping_address;
    shippingDiv.innerHTML = `
        <p class="mb-1"><strong>${shipping.name}</strong></p>
        <p class="mb-1">${shipping.address}</p>
        <p class="mb-1">${shipping.city}, ${shipping.state} ${shipping.postal_code}</p>
        <p class="mb-0">${shipping.phone}</p>
    `;

    // Display order summary
    const summaryDiv = document.getElementById('orderSummary');
    summaryDiv.innerHTML = `
        <div class="d-flex justify-content-between mb-2">
            <span>Total Amount:</span>
            <span class="fw-bold">₹${parseFloat(order.total_amount).toLocaleString()}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <span>Payment Status:</span>
            <span class="badge bg-success">Paid</span>
        </div>
    `;
}
</script>
@endpush
